
> wizlop@0.1.0 build:webpack
> webpack --config config/webpack/webpack.dev.js

asset bundle.js 4.58 MiB [emitted] [big] (name: main)
runtime modules 1.04 KiB 5 modules
modules by path ./node_modules/ 1.48 MiB 109 modules
modules by path ./app/ 147 KiB
  modules by path ./app/landing/components/ 113 KiB 14 modules
  modules by path ./app/landing/utils/*.ts 13.3 KiB
    ./app/landing/utils/scrollAnimations.ts 4.46 KiB [built] [code generated]
    ./app/landing/utils/responsiveUtils.ts 8.86 KiB [built] [code generated]
  ./app/page.tsx 1.13 KiB [built] [code generated]
  ./app/colors.ts 3.55 KiB [built] [code generated]
  ./app/shared/poi/constants.ts 15.9 KiB [built] [code generated]

WARNING in ./app/landing/components/hero/HeroSection.tsx 29:199-211
export 'default' (imported as 'HeroBranding') was not found in './HeroBranding' (module has no exports)
    at HarmonyImportSpecifierDependency.getLinkingErrors (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/webpack/lib/dependencies/HarmonyImportDependency.js:183:8)
    at HarmonyImportSpecifierDependency._getErrors (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/webpack/lib/dependencies/HarmonyImportSpecifierDependency.js:260:15)
    at HarmonyImportSpecifierDependency.getWarnings (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/webpack/lib/dependencies/HarmonyImportSpecifierDependency.js:236:16)
    at Compilation.reportDependencyErrorsAndWarnings (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/webpack/lib/Compilation.js:3450:24)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/webpack/lib/Compilation.js:3008:28
    at eval (eval at create (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/tapable/lib/HookCodeFactory.js:33:10), <anonymous>:29:1)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/webpack/lib/FlagDependencyExportsPlugin.js:404:10
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/neo-async/async.js:2830:7
    at Object.each (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/neo-async/async.js:2850:39)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/webpack/lib/FlagDependencyExportsPlugin.js:374:17

ModuleDependencyWarning: export 'default' (imported as 'HeroBranding') was not found in './HeroBranding' (module has no exports)
    at Compilation.reportDependencyErrorsAndWarnings (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/webpack/lib/Compilation.js:3453:23)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/webpack/lib/Compilation.js:3008:28
    at eval (eval at create (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/tapable/lib/HookCodeFactory.js:33:10), <anonymous>:29:1)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/webpack/lib/FlagDependencyExportsPlugin.js:404:10
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/neo-async/async.js:2830:7
    at Object.each (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/neo-async/async.js:2850:39)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/webpack/lib/FlagDependencyExportsPlugin.js:374:17
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/neo-async/async.js:2830:7
    at Object.each (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/neo-async/async.js:2850:39)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/webpack/lib/FlagDependencyExportsPlugin.js:52:15
 @ ./app/landing/components/index.ts 7:0-60 7:0-60
 @ ./app/page.tsx 6:0-51 22:16-27

WARNING in ./app/landing/components/index.ts 6:0-62
export 'default' (reexported as 'HeroBranding') was not found in './hero/HeroBranding' (module has no exports)
    at HarmonyExportImportedSpecifierDependency.getLinkingErrors (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/webpack/lib/dependencies/HarmonyImportDependency.js:183:8)
    at HarmonyExportImportedSpecifierDependency._getErrors (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/webpack/lib/dependencies/HarmonyExportImportedSpecifierDependency.js:864:21)
    at HarmonyExportImportedSpecifierDependency.getWarnings (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/webpack/lib/dependencies/HarmonyExportImportedSpecifierDependency.js:840:16)
    at Compilation.reportDependencyErrorsAndWarnings (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/webpack/lib/Compilation.js:3450:24)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/webpack/lib/Compilation.js:3008:28
    at eval (eval at create (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/tapable/lib/HookCodeFactory.js:33:10), <anonymous>:29:1)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/webpack/lib/FlagDependencyExportsPlugin.js:404:10
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/neo-async/async.js:2830:7
    at Object.each (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/neo-async/async.js:2850:39)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/webpack/lib/FlagDependencyExportsPlugin.js:374:17

ModuleDependencyWarning: export 'default' (reexported as 'HeroBranding') was not found in './hero/HeroBranding' (module has no exports)
    at Compilation.reportDependencyErrorsAndWarnings (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/webpack/lib/Compilation.js:3453:23)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/webpack/lib/Compilation.js:3008:28
    at eval (eval at create (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/tapable/lib/HookCodeFactory.js:33:10), <anonymous>:29:1)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/webpack/lib/FlagDependencyExportsPlugin.js:404:10
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/neo-async/async.js:2830:7
    at Object.each (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/neo-async/async.js:2850:39)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/webpack/lib/FlagDependencyExportsPlugin.js:374:17
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/neo-async/async.js:2830:7
    at Object.each (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/neo-async/async.js:2850:39)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/webpack/lib/FlagDependencyExportsPlugin.js:52:15
 @ ./app/page.tsx 6:0-51 22:16-27

ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/landing/components/index.ts
./app/landing/components/index.ts 8:9-16
[tsl] ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/landing/components/index.ts(8,10)
      TS2305: Module '"./hero/HeroBranding"' has no exported member 'default'.
ts-loader-default_e552be7a13a4e4fd
Error: [tsl] ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/landing/components/index.ts(8,10)
      TS2305: Module '"./hero/HeroBranding"' has no exported member 'default'.
    at makeError (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:105:19)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:77:27
    at Array.map (<anonymous>)
    at formatErrors (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:60:14)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:152:66
    at Array.forEach (<anonymous>)
    at provideErrorsToWebpack (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:149:31)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:36:9
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/instances.js:214:13
    at fn (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/webpack/lib/Compilation.js:548:10)
 @ ./app/page.tsx 6:0-51 22:16-27

ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/landing/components/hero/HeroBranding.tsx
./app/landing/components/hero/HeroBranding.tsx 54:3-6
[tsl] ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/landing/components/hero/HeroBranding.tsx(54,4)
      TS17008: JSX element 'div' has no corresponding closing tag.
ts-loader-default_e552be7a13a4e4fd
Error: [tsl] ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/landing/components/hero/HeroBranding.tsx(54,4)
      TS17008: JSX element 'div' has no corresponding closing tag.
    at makeError (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:105:19)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:77:27
    at Array.map (<anonymous>)
    at formatErrors (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:60:14)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:152:66
    at Array.forEach (<anonymous>)
    at provideErrorsToWebpack (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:149:31)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:36:9
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/instances.js:214:13
    at fn (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/webpack/lib/Compilation.js:548:10)
 @ ./app/landing/components/index.ts 6:0-62 6:0-62
 @ ./app/page.tsx 6:0-51 22:16-27

ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/landing/components/hero/HeroBranding.tsx
./app/landing/components/hero/HeroBranding.tsx 498:0-1
[tsl] ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/landing/components/hero/HeroBranding.tsx(498,1)
      TS1381: Unexpected token. Did you mean `{'}'}` or `&rbrace;`?
ts-loader-default_e552be7a13a4e4fd
Error: [tsl] ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/landing/components/hero/HeroBranding.tsx(498,1)
      TS1381: Unexpected token. Did you mean `{'}'}` or `&rbrace;`?
    at makeError (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:105:19)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:77:27
    at Array.map (<anonymous>)
    at formatErrors (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:60:14)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:152:66
    at Array.forEach (<anonymous>)
    at provideErrorsToWebpack (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:149:31)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:36:9
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/instances.js:214:13
    at fn (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/webpack/lib/Compilation.js:548:10)
 @ ./app/landing/components/index.ts 6:0-62 6:0-62
 @ ./app/page.tsx 6:0-51 22:16-27

ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/landing/components/hero/HeroBranding.tsx
./app/landing/components/hero/HeroBranding.tsx 501:0
[tsl] ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/landing/components/hero/HeroBranding.tsx(501,1)
      TS1005: '</' expected.
ts-loader-default_e552be7a13a4e4fd
Error: [tsl] ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/landing/components/hero/HeroBranding.tsx(501,1)
      TS1005: '</' expected.
    at makeError (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:105:19)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:77:27
    at Array.map (<anonymous>)
    at formatErrors (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:60:14)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:152:66
    at Array.forEach (<anonymous>)
    at provideErrorsToWebpack (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:149:31)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:36:9
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/instances.js:214:13
    at fn (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/webpack/lib/Compilation.js:548:10)
 @ ./app/landing/components/index.ts 6:0-62 6:0-62
 @ ./app/page.tsx 6:0-51 22:16-27

ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/landing/components/hero/HeroSection.tsx
./app/landing/components/hero/HeroSection.tsx 9:7-19
[tsl] ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/landing/components/hero/HeroSection.tsx(9,8)
      TS1192: Module '"/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/landing/components/hero/HeroBranding"' has no default export.
ts-loader-default_e552be7a13a4e4fd
Error: [tsl] ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/landing/components/hero/HeroSection.tsx(9,8)
      TS1192: Module '"/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/landing/components/hero/HeroBranding"' has no default export.
    at makeError (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:105:19)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:77:27
    at Array.map (<anonymous>)
    at formatErrors (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:60:14)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:152:66
    at Array.forEach (<anonymous>)
    at provideErrorsToWebpack (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:149:31)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:36:9
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/instances.js:214:13
    at fn (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/webpack/lib/Compilation.js:548:10)
 @ ./app/landing/components/index.ts 7:0-60 7:0-60
 @ ./app/page.tsx 6:0-51 22:16-27

webpack 5.100.1 compiled with 5 errors and 2 warnings in 3571 ms
