/* Modern POI Page Styles */

/* Smooth animations for card entrance */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInFromLeft {
  from {
    opacity: 0;
    transform: translateX(-30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slideInFromRight {
  from {
    opacity: 0;
    transform: translateX(30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/* Pulse animation for loading states */
@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

/* Floating animation for decorative elements */
@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

/* Gradient text animation */
@keyframes gradientShift {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

/* Modern card hover effects */
.poi-card {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.poi-card:hover {
  transform: translateY(-8px) scale(1.02);
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
}

/* Smooth text transitions */
.poi-title {
  transition: color 0.2s ease-in-out;
}

.poi-title:hover {
  background: linear-gradient(135deg, #33C2FF 0%, #80ED99 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* Modern button styles */
.modern-button {
  position: relative;
  overflow: hidden;
  transition: all 0.3s ease;
}

.modern-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.modern-button:hover::before {
  left: 100%;
}

/* Glassmorphism effect */
.glass-effect {
  background: rgba(255, 255, 255, 0.25);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.18);
}

/* Custom scrollbar */
.custom-scrollbar::-webkit-scrollbar {
  width: 6px;
}

.custom-scrollbar::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.1);
  border-radius: 3px;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
  background: linear-gradient(45deg, #3B82F6, #06B6D4);
  border-radius: 3px;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(45deg, #2563EB, #0891B2);
}

/* Text clamp utilities */
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.line-clamp-3 {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* Responsive grid improvements */
@media (max-width: 768px) {
  .poi-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }
}

@media (min-width: 769px) and (max-width: 1024px) {
  .poi-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 1.5rem;
  }
}

@media (min-width: 1025px) and (max-width: 1280px) {
  .poi-grid {
    grid-template-columns: repeat(3, 1fr);
    gap: 2rem;
  }
}

@media (min-width: 1281px) {
  .poi-grid {
    grid-template-columns: repeat(4, 1fr);
    gap: 2rem;
  }
}

/* Loading skeleton animation */
@keyframes shimmer {
  0% {
    background-position: -468px 0;
  }
  100% {
    background-position: 468px 0;
  }
}

.skeleton {
  animation: shimmer 1.2s ease-in-out infinite;
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 400% 100%;
}

/* Modern focus states */
.modern-focus:focus {
  outline: none;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.5);
  border-color: #3B82F6;
}

/* Smooth transitions for all interactive elements */
* {
  transition: color 0.2s ease, background-color 0.2s ease, border-color 0.2s ease, transform 0.2s ease, box-shadow 0.2s ease;
}

/* Disable transitions during page load */
.preload * {
  transition: none !important;
}

/* Media gallery specific styles */
.media-grid-item {
  opacity: 0;
  animation: fadeInUp 0.6s ease-out forwards;
}

/* Staggered animation for media items */
.media-grid-item:nth-child(1) { animation-delay: 0ms; }
.media-grid-item:nth-child(2) { animation-delay: 100ms; }
.media-grid-item:nth-child(3) { animation-delay: 200ms; }
.media-grid-item:nth-child(4) { animation-delay: 300ms; }
.media-grid-item:nth-child(5) { animation-delay: 400ms; }
.media-grid-item:nth-child(6) { animation-delay: 500ms; }
.media-grid-item:nth-child(7) { animation-delay: 600ms; }
.media-grid-item:nth-child(8) { animation-delay: 700ms; }
.media-grid-item:nth-child(9) { animation-delay: 800ms; }
.media-grid-item:nth-child(10) { animation-delay: 900ms; }

/* Hover effects for media items */
.media-item-hover {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.media-item-hover:hover {
  transform: translateY(-8px) scale(1.02);
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
}

/* Enhanced POI Page Design */
.poi-page-container {
  width: 100%;
  max-width: none;
  margin: 0;
  padding: 0;
}

.poi-filter-container {
  backdrop-filter: blur(20px);
  background: rgba(255, 255, 255, 0.95);
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

.poi-card-enhanced {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.poi-card-enhanced:hover {
  transform: translateY(-4px);
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
}

/* Enhanced filter layout animations */
.poi-filter-compact {
  animation: slideInFromBottom 0.6s ease-out;
}

@keyframes slideInFromBottom {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Enhanced title animations */
.poi-title-enhanced {
  animation: slideInFromLeft 0.8s ease-out;
}

/* Filter grid enhancements */
.poi-filter-grid {
  animation: staggerIn 0.8s ease-out;
}

@keyframes staggerIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Enhanced search input */
.poi-search-enhanced {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.poi-search-enhanced:focus {
  transform: translateY(-2px);
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

/* Responsive enhancements */
@media (max-width: 1024px) {
  .poi-filter-compact {
    flex-direction: column;
  }

  .poi-filter-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 640px) {
  .poi-filter-grid {
    grid-template-columns: 1fr;
  }
}

/* Clean single-row filter design */
.poi-filter-clean {
  background: transparent;
  border: none;
  box-shadow: none;
}

.poi-filter-clean select,
.poi-filter-clean input {
  font-size: 0.75rem;
  padding: 0.5rem 0.75rem;
  border: 1px solid #E5E7EB;
  background: #FFFFFF;
  border-radius: 0.5rem;
  transition: all 0.3s ease;
}

.poi-filter-clean select {
  min-width: 110px;
  max-width: 140px;
}

.poi-filter-clean input {
  width: 200px;
  padding-left: 2rem;
  padding-right: 1rem;
}

/* List view styles */
.poi-grid.list-view {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.poi-grid.list-view .poi-card {
  width: 100%;
  max-width: none;
}

/* Enhanced responsive behavior for single-row layout */
@media (max-width: 1024px) {
  .poi-filter-clean .flex-wrap {
    flex-wrap: wrap;
    gap: 0.5rem;
  }

  .poi-filter-clean select {
    min-width: 100px;
    max-width: 120px;
  }

  .poi-filter-clean input {
    width: 180px;
  }
}

@media (max-width: 768px) {
  .poi-filter-clean .flex-wrap {
    flex-direction: column;
    gap: 0.75rem;
  }

  .poi-filter-clean select,
  .poi-filter-clean input {
    width: 100%;
    min-width: auto;
    max-width: none;
  }
}
