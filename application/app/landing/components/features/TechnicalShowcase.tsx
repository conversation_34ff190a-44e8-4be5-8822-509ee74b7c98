/** @format */

'use client';

import { colors } from '@/app/colors';
import { useViewportHeight } from '@/app/landing/utils/responsiveUtils';
import { LANDING_PAGE_DATA } from '@/app/shared/poi/constants';
import React from 'react';
import { FiActivity, FiCpu, FiTarget, FiZap } from 'react-icons/fi';

interface TechnicalShowcaseProps {
	onGetStarted: () => void;
}

interface TechFeature {
	id: string;
	icon: React.ReactNode;
	title: string;
	subtitle: string;
	description: string;
	metrics: string;
	color: string;
	gradient: string;
}

const TechnicalShowcase: React.FC<TechnicalShowcaseProps> = ({
	onGetStarted,
}) => {
	const { screenSize, minHeight, padding } = useViewportHeight('section');

	const techFeatures: TechFeature[] = [
		{
			id: 'spatial-engine',
			icon: <FiZap className='w-6 h-6' />,
			title: 'Lightning-Fast Spatial Engine',
			subtitle: 'PostGIS-Powered Search',
			description:
				'Advanced spatial database with PostGIS indexing delivers sub-second search results across millions of locations with precision distance calculations.',
			metrics: '< 0.1s response time',
			color: colors.brand.blue,
			gradient: `linear-gradient(135deg, ${colors.brand.blue} 0%, ${colors.supporting.lightBlue} 100%)`,
		},
		{
			id: 'geospatial-algorithms',
			icon: <FiTarget className='w-6 h-6' />,
			title: 'Precision Location Matching',
			subtitle: 'Advanced Geospatial Algorithms',
			description:
				'Intelligent boundary detection, coordinate-based proximity search, and smart location resolution with administrative boundary support.',
			metrics: 'Meter-level accuracy',
			color: colors.brand.green,
			gradient: `linear-gradient(135deg, ${colors.brand.green} 0%, ${colors.supporting.mintGreen} 100%)`,
		},
		{
			id: 'top-candidates',
			icon: <FiCpu className='w-6 h-6' />,
			title: 'Smart Ranking Algorithms',
			subtitle: 'Top Candidate Selection',
			description:
				'Sophisticated ranking algorithms that consider distance, relevance, user preferences, and contextual factors to surface the best matches.',
			metrics: 'Multi-factor scoring',
			color: colors.brand.navy,
			gradient: `linear-gradient(135deg, ${colors.brand.navy} 0%, ${colors.supporting.darkBlue} 100%)`,
		},
		{
			id: 'ai-enhancement',
			icon: <FiActivity className='w-6 h-6' />,
			title: 'AI-Enhanced Discovery',
			subtitle: 'Natural Language Processing',
			description:
				'AI agents work alongside our spatial algorithms to understand context, process natural language queries, and provide personalized recommendations.',
			metrics: 'Context-aware results',
			color: colors.supporting.purple,
			gradient: `linear-gradient(135deg, ${colors.supporting.purple} 0%, ${colors.brand.blue} 100%)`,
		},
	];

	return (
		<div
			className='bg-transparent viewport-section relative'
			style={{
				minHeight,
				paddingLeft: 0,
				paddingRight: 0,
				paddingTop: padding,
				paddingBottom: '120px',
			}}>
			{/* Background Elements */}
			<div className='absolute inset-0 opacity-5'>
				<div
					className='absolute top-1/4 left-1/4 w-32 h-32 rounded-full animate-pulse'
					style={{
						background: `radial-gradient(circle, ${colors.brand.blue}40 0%, transparent 70%)`,
						animationDuration: '4s',
					}}
				/>
				<div
					className='absolute bottom-1/3 right-1/4 w-24 h-24 rounded-lg animate-pulse'
					style={{
						background: `linear-gradient(45deg, ${colors.brand.green}30 0%, transparent 70%)`,
						transform: 'rotate(45deg)',
						animationDuration: '3s',
						animationDelay: '1s',
					}}
				/>
			</div>

			<div className='relative z-10 px-6 lg:px-12'>
				{/* Section Header */}
				<div className='text-center mb-12'>
					<div
						className='inline-block px-4 py-2 rounded-full text-sm font-medium mb-4'
						style={{
							background: `linear-gradient(135deg, ${colors.ui.blue50} 0%, ${colors.ui.green50} 100%)`,
							color: colors.brand.navy,
						}}>
						{LANDING_PAGE_DATA.sectionHeaders.spatialTech.badge}
					</div>
					<h2
						className='text-3xl lg:text-4xl font-black mb-4'
						style={{ color: colors.neutral.textBlack }}>
						<span style={{ color: colors.brand.navy }}>
							{LANDING_PAGE_DATA.sectionHeaders.spatialTech.title}
						</span>{' '}
						<span style={{ color: colors.brand.blue }}>
							{LANDING_PAGE_DATA.sectionHeaders.spatialTech.subtitle}
						</span>
					</h2>
					<p
						className='text-lg max-w-3xl mx-auto leading-relaxed'
						style={{ color: colors.neutral.slateGray }}>
						{LANDING_PAGE_DATA.sectionHeaders.spatialTech.description}
					</p>
				</div>

				{/* Technical Features Grid */}
				<div className='grid grid-cols-1 lg:grid-cols-2 gap-8 max-w-6xl mx-auto'>
					{techFeatures.map((feature, index) => (
						<div
							key={feature.id}
							className='relative p-6 rounded-2xl border backdrop-blur-sm'
							style={{
								background: `linear-gradient(135deg, ${colors.neutral.cloudWhite}95 0%, ${colors.ui.blue50}30 100%)`,
								borderColor: colors.ui.gray200,
								boxShadow: '0 8px 32px rgba(51, 194, 255, 0.08)',
							}}>
							{/* Feature Icon */}
							<div
								className='w-16 h-16 rounded-xl flex items-center justify-center mb-4'
								style={{
									background: feature.gradient,
									color: 'white',
								}}>
								{feature.icon}
							</div>

							{/* Feature Content */}
							<div>
								<h3
									className='text-xl font-bold mb-2'
									style={{ color: colors.brand.navy }}>
									{feature.title}
								</h3>
								<p
									className='text-sm font-medium mb-3'
									style={{ color: feature.color }}>
									{feature.subtitle}
								</p>
								<p
									className='text-base leading-relaxed mb-4'
									style={{ color: colors.neutral.slateGray }}>
									{feature.description}
								</p>
								<div
									className='inline-block px-3 py-1 rounded-full text-sm font-medium'
									style={{
										background: `${feature.color}15`,
										color: feature.color,
									}}>
									{feature.metrics}
								</div>
							</div>
						</div>
					))}
				</div>
			</div>
		</div>
	);
};

export default TechnicalShowcase;
