/** @format */

/* Viewport height utilities for better cross-browser support */

/* Fix for mobile browsers where 100vh doesn't account for address bar */
.viewport-height-full {
  height: 100vh;
  min-height: 100vh;
  /* Fallback for older browsers */
  min-height: -webkit-fill-available;
}

/* Support for dynamic viewport units in newer browsers */
@supports (height: 100dvh) {
  .viewport-height-full {
    height: 100dvh;
    min-height: 100dvh;
  }
}

/* Ensure content doesn't overflow on small screens */
.viewport-content {
  max-height: 100vh;
  overflow-y: auto;
}

/* Responsive viewport sections */
.viewport-section {
  min-height: 80vh;
  max-height: 100vh;
  overflow: hidden;
}

@media (min-width: 768px) {
  .viewport-section {
    min-height: 85vh;
  }
}

@media (min-width: 1024px) {
  .viewport-section {
    min-height: 90vh;
  }
}

/* Constrained content areas */
.viewport-constrained {
  max-height: calc(100vh - 100px);
  overflow-y: auto;
  overflow-x: hidden;
}

/* Flexible layout containers */
.flex-container-constrained {
  display: flex;
  flex-direction: column;
  height: 100%;
  min-height: 0;
}

.flex-item-constrained {
  flex: 1;
  min-height: 0;
  overflow: hidden;
}

/* Scrollable content areas */
.scrollable-content {
  overflow-y: auto;
  overflow-x: hidden;
  max-height: 100%;
}

/* Grid layout constraints */
.grid-constrained {
  display: grid;
  height: 100%;
  min-height: 0;
}

/* Centered container utility */
.container-centered {
  margin-left: auto;
  margin-right: auto;
  width: 100%;
  max-width: 80rem; /* 1280px - same as max-w-7xl */
  padding-left: 1rem;
  padding-right: 1rem;
}

@media (min-width: 640px) {
  .container-centered {
    padding-left: 1.5rem;
    padding-right: 1.5rem;
  }
}

@media (min-width: 1024px) {
  .container-centered {
    padding-left: 2rem;
    padding-right: 2rem;
  }
}

/* Fix for iOS Safari viewport issues */
@supports (-webkit-touch-callout: none) {
  .viewport-height-full {
    min-height: -webkit-fill-available;
  }
}

/* Ensure proper scaling on orientation change */
@media screen and (orientation: landscape) {
  .viewport-height-full {
    height: 100vh;
    min-height: 100vh;
  }
}

/* Prevent content from being cut off on very small screens */
@media (max-height: 500px) {
  .viewport-height-full {
    min-height: 500px;
  }
}
