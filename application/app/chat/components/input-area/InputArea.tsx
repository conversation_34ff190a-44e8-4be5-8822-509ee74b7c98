/** @format */

import { zIndexLayers } from '@/app/chat/styles';
import { BottomBarProps } from '@/app/chat/types';
import { colors } from '@/app/colors';
import React from 'react';
import { FaMicrophone } from 'react-icons/fa';
import { CreditStatus } from '../credit-status';

const InputArea: React.FC<BottomBarProps> = ({
	newMessage,
	handleInputChange,
	handleKeyDown,
	inputRef,
	isSending,
	hasMessages,
}) => {
	const isChatting = newMessage.length > 0 || isSending || hasMessages;

	const handleInputFocus = () => {
		// Don't change to chatting mode on focus, only on actual typing/enter
	};

	return (
		<div
			className={`transition-all duration-700 ease-in-out px-5 flex justify-center ${
				zIndexLayers.bottomBar
			} ${
				isChatting
					? 'items-end pb-5 h-auto min-h-[100px]'
					: 'items-center relative h-screen pb-0'
			}`}>
			<div
				className={`w-full max-w-[800px] flex flex-col items-center transition-all duration-700 ease-out ${
					isChatting ? 'translate-y-0' : ''
				}`}>
				{!isChatting && (
					<div
						className='text-3xl font-bold mb-12 text-center transition-opacity duration-500'
						style={{ color: colors.neutral.textBlack }}>
						Where do you want to explore today?
					</div>
				)}

				{/* Input Area - Fixed positioning and layout */}
				<div className='w-full relative flex items-center'>
					<div className='w-full relative'>
						<textarea
							ref={inputRef}
							value={newMessage}
							onChange={(e) => {
								handleInputChange(e);
								// Auto-resize
								if (inputRef.current) {
									inputRef.current.style.height = 'auto';
									inputRef.current.style.height = `${Math.min(
										inputRef.current.scrollHeight,
										120
									)}px`;
								}
							}}
							onKeyDown={handleKeyDown}
							onFocus={(e) => {
								handleInputFocus();
								e.currentTarget.style.borderColor = colors.brand.blue;
								e.currentTarget.style.boxShadow = `0 0 0 2px ${colors.brand.blue}40`;
							}}
							onBlur={(e) => {
								e.currentTarget.style.borderColor = colors.ui.blue200;
								e.currentTarget.style.boxShadow = 'none';
							}}
							placeholder={
								isChatting
									? 'Type your message...'
									: 'Ask me about any place in the world...'
							}
							disabled={isSending}
							className='w-full p-4 pr-24 rounded-2xl border resize-none focus:outline-none transition-all hide-scrollbar'
							style={{
								backgroundColor: colors.neutral.cloudWhite,
								borderColor: colors.ui.blue200,
								color: colors.neutral.textBlack,
								minHeight: '56px',
								maxHeight: '120px',
								fontSize: '16px', // Prevent zoom on iOS
								lineHeight: '1.5',
								overflowY: 'auto',
								scrollbarWidth: 'none', // Firefox
							}}
							rows={1}
						/>
						{/* Hide scrollbar for Webkit browsers */}
						<style>{`
              .hide-scrollbar::-webkit-scrollbar {
                display: none;
              }
            `}</style>

						{/* Input Buttons Container - Perfectly Centered */}
						<div
							className='absolute right-3 flex items-center gap-3'
							style={{
								top: '50%',
								transform: 'translateY(-50%)',
								marginTop: '-2px', // Fine-tune vertical alignment
							}}>
							{/* Microphone Button - Non-functional placeholder */}
							<button
								onClick={() => {
									// Non-functional placeholder - just visual feedback
									console.log('Voice input coming soon...');
								}}
								disabled={true}
								className='w-10 h-10 rounded-full transition-all duration-300 flex items-center justify-center shadow-lg opacity-50 cursor-not-allowed'
								style={{
									background: `linear-gradient(135deg, ${colors.ui.gray200} 0%, ${colors.ui.gray300} 100%)`,
									color: colors.neutral.slateGray,
									border: `1px solid ${colors.ui.gray400}`,
								}}
								title='Voice input (coming soon)'>
								<FaMicrophone size={16} />
							</button>

							{/* Send Button - Fully functional */}
							<button
								onClick={() => {
									console.log(
										'Send button clicked, newMessage:',
										newMessage,
										'isSending:',
										isSending
									);
									if (newMessage && newMessage.trim() && !isSending) {
										// Create a synthetic keyboard event to trigger the send
										const syntheticEvent = {
											key: 'Enter',
											shiftKey: false,
											preventDefault: () => {},
											stopPropagation: () => {},
										} as React.KeyboardEvent<HTMLTextAreaElement>;
										handleKeyDown(syntheticEvent);
									}
								}}
								disabled={!newMessage || !newMessage.trim() || isSending}
								className='w-10 h-10 rounded-full transition-all duration-300 flex items-center justify-center shadow-lg hover:shadow-xl disabled:opacity-50 disabled:cursor-not-allowed'
								style={{
									background:
										newMessage && newMessage.trim() && !isSending
											? `linear-gradient(135deg, ${colors.brand.blue} 0%, ${colors.supporting.lightBlue} 100%)`
											: `linear-gradient(135deg, ${colors.ui.gray300} 0%, ${colors.ui.gray400} 100%)`,
									color: 'white',
									border:
										newMessage && newMessage.trim() && !isSending
											? `1px solid ${colors.brand.blue}`
											: `1px solid ${colors.ui.gray400}`,
								}}
								onMouseEnter={(e) => {
									if (newMessage && newMessage.trim() && !isSending) {
										e.currentTarget.style.background = `linear-gradient(135deg, ${colors.brand.green} 0%, ${colors.brand.blue} 100%)`;
										e.currentTarget.style.transform = 'scale(1.1)';
										e.currentTarget.style.borderColor = colors.brand.green;
									}
								}}
								onMouseLeave={(e) => {
									if (newMessage && newMessage.trim() && !isSending) {
										e.currentTarget.style.background = `linear-gradient(135deg, ${colors.brand.blue} 0%, ${colors.supporting.lightBlue} 100%)`;
										e.currentTarget.style.transform = 'scale(1)';
										e.currentTarget.style.borderColor = colors.brand.blue;
									}
								}}>
								{isSending ? (
									<div className='w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin' />
								) : (
									<span className='text-lg font-bold'>→</span>
								)}
							</button>
						</div>
					</div>
				</div>

				{/* Credit Status */}
				<div className='mt-2 flex justify-center'>
					<CreditStatus compact={true} />
				</div>
			</div>
		</div>
	);
};

export default InputArea;
