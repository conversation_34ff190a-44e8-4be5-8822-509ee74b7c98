/** @format */

@tailwind base;
@tailwind components;
@tailwind utilities;

/* Dynamic Layout Styles */
@import './landing/styles/dynamicLayout.css';

/* Base styles */
* {
	box-sizing: border-box;
	padding: 0;
	margin: 0;
}

html,
body,
#__next {
	height: 100%;
	max-width: 100vw;
	overflow-x: hidden;
	font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
		'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
		sans-serif;
	-webkit-font-smoothing: antialiased;
	-moz-osx-font-smoothing: grayscale;
}

/* Custom scrollbar for webkit browsers */
::-webkit-scrollbar {
	width: 8px;
}

::-webkit-scrollbar-track {
	background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
	background: #c1c1c1;
	border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
	background: #a8a8a8;
}

/* Hero Animation Keyframes */
@keyframes shimmer {
	0% {
		transform: translateX(-100%);
	}
	100% {
		transform: translateX(100%);
	}
}

/* Smooth transitions for all elements */
* {
	transition: color 0.2s ease, background-color 0.2s ease,
		border-color 0.2s ease, transform 0.2s ease, box-shadow 0.2s ease;
}

/* Custom gradient text - using centralized colors */
.gradient-text {
	background: linear-gradient(135deg, #38a3a5, #57cc99);
	-webkit-background-clip: text;
	-webkit-text-fill-color: transparent;
	background-clip: text;
}

/* Wizlop brand gradient */
.wizlop-gradient {
	background: linear-gradient(135deg, #22577a 0%, #38a3a5 50%, #57cc99 100%);
}

/* Wizlop text gradient */
.wizlop-text-gradient {
	background: linear-gradient(135deg, #22577a 0%, #38a3a5 100%);
	-webkit-background-clip: text;
	-webkit-text-fill-color: transparent;
	background-clip: text;
}

/* Animation delay utilities */
.animate-delay-100 {
	animation-delay: 0.1s;
}
.animate-delay-200 {
	animation-delay: 0.2s;
}
.animate-delay-300 {
	animation-delay: 0.3s;
}
.animate-delay-400 {
	animation-delay: 0.4s;
}
.animate-delay-500 {
	animation-delay: 0.5s;
}
.animate-delay-1000 {
	animation-delay: 1s;
}
.animate-delay-1500 {
	animation-delay: 1.5s;
}

/* Dynamic height utilities */
.h-screen-or-auto {
	height: 100vh;
}
.h-screen-or-auto.is-chatting {
	height: auto;
	max-height: 205px;
}

/* Textarea height utilities */
.textarea-dynamic {
	min-height: 60px;
	max-height: 120px;
}

/* Additional Tailwind utilities for better responsive design */
@layer utilities {
	.h-screen-safe {
		height: 100vh;
		height: 100dvh; /* Dynamic viewport height for mobile */
	}

	.max-h-screen-safe {
		max-height: 100vh;
		max-height: 100dvh;
	}
}

/* Map marker animations */
@keyframes pulse {
	0% {
		transform: scale(1);
		opacity: 1;
	}
	50% {
		transform: scale(1.2);
		opacity: 0.7;
	}
	100% {
		transform: scale(1);
		opacity: 1;
	}
}

.user-location-marker,
.extracted-location-marker {
	z-index: 1000;
}

/* Leaflet styles - only load when needed */
.leaflet-container {
	height: 100% !important;
	width: 100% !important;
	position: relative !important;
	z-index: 1 !important;
}

/* Ensure map tiles load properly */
.leaflet-tile-pane {
	z-index: 1 !important;
}

/* Location link styles in chat messages */
[data-location-id] {
	color: #2563eb !important;
	cursor: pointer !important;
	text-decoration: underline !important;
	font-weight: 600 !important;
	transition: color 0.2s ease !important;
}

[data-location-id]:hover {
	color: #1d4ed8 !important;
	text-decoration: underline !important;
}

/* Ensure map controls are accessible */
.leaflet-control-container {
	z-index: 1000 !important;
}

/* Custom animations */
@keyframes fadeIn {
	from {
		opacity: 0;
	}
	to {
		opacity: 1;
	}
}

@keyframes slideUp {
	from {
		opacity: 0;
		transform: translateY(30px);
	}
	to {
		opacity: 1;
		transform: translateY(0);
	}
}

@keyframes bounceGentle {
	0%,
	20%,
	50%,
	80%,
	100% {
		transform: translateY(0);
	}
	40% {
		transform: translateY(-10px);
	}
	60% {
		transform: translateY(-5px);
	}
}

@keyframes slideDown {
	from {
		opacity: 0;
		transform: translateY(-50px);
	}
	to {
		opacity: 1;
		transform: translateY(0);
	}
}

@keyframes scaleIn {
	from {
		opacity: 0;
		transform: scale(0.8);
	}
	to {
		opacity: 1;
		transform: scale(1);
	}
}

@keyframes globeExpand {
	from {
		transform: scale(0.3) translateY(100px);
		opacity: 0.3;
	}
	to {
		transform: scale(1) translateY(0);
		opacity: 1;
	}
}

.animate-fade-in {
	animation: fadeIn 0.8s ease-in-out;
}

.animate-slide-up {
	animation: slideUp 0.8s ease-out;
}

.animate-slide-down {
	animation: slideDown 0.6s ease-out;
}

.animate-scale-in {
	animation: scaleIn 0.5s ease-out;
}

.animate-globe-expand {
	animation: globeExpand 1s ease-out;
}

.animate-bounce-gentle {
	animation: bounceGentle 3s ease-in-out infinite;
}

/* Globe specific styles */
.globe-container {
	width: 100% !important;
	height: 100% !important;
}

.globe-container canvas {
	width: 100% !important;
	height: 100% !important;
	object-fit: cover;
}

/* Navigation spacing utilities */
.nav-spacing {
	padding-top: 4rem; /* 64px - height of navigation bar */
}

.nav-spacing-mobile {
	padding-top: 3.5rem; /* 56px - height of mobile navigation bar */
}

/* AI Showcase Animations */
@keyframes fadeIn {
	from {
		opacity: 0;
		transform: translateY(20px);
	}
	to {
		opacity: 1;
		transform: translateY(0);
	}
}

@keyframes slideInUp {
	from {
		opacity: 0;
		transform: translateY(30px);
	}
	to {
		opacity: 1;
		transform: translateY(0);
	}
}

.animate-fadeIn {
	animation: fadeIn 0.6s ease-out forwards;
}

.animate-slideInUp {
	animation: slideInUp 0.6s ease-out forwards;
}
