/** @format */

'use client';

import React from 'react';

interface DynamicSectionContainerProps {
	sectionType: 'hero' | 'section1' | 'section2' | 'footer';
	children: React.ReactNode;
	className?: string;
	style?: React.CSSProperties;
}

const DynamicSectionContainer: React.FC<DynamicSectionContainerProps> = ({
	sectionType,
	children,
	className = '',
	style = {},
}) => {
	// Use responsive container approach following the rules
	// Mobile (0-767px): w-full px-4 (16px padding)
	// Tablet (768-1023px): w-full px-6 (24px padding)
	// Desktop (1024px+): max-w-7xl mx-auto px-12 (48px padding, centered)
	const responsiveContainerClass =
		'w-full px-4 md:px-6 lg:max-w-7xl lg:mx-auto lg:px-12';

	return (
		<div
			className={`${className}`}
			style={style}>
			<div className={responsiveContainerClass}>{children}</div>
		</div>
	);
};

export default DynamicSectionContainer;
