/** @format */

import { Icon<PERSON>utton, ModalOverlay } from 'app/chat/components/ui';
import { sidebarStyles, zIndexLayers } from 'app/chat/styles';
import { RightSidebarProps } from 'app/chat/types';
import { colors } from 'app/colors';
import React, {
	useCallback,
	useEffect,
	useMemo,
	useRef,
	useState,
} from 'react';
import DynamicLeafletMap from './DynamicLeafletMap';

const RightSidebar: React.FC<RightSidebarProps> = ({
	isOpen,
	onClose,
	userLocation,
	extractedLocations,
}) => {
	// Debug: Log when locations are received
	if (extractedLocations?.length > 0) {
		console.log(
			'🗺️ RightSidebar displaying',
			extractedLocations.length,
			'locations'
		);
	}
	const [rightSidebarWidth, setRightSidebarWidth] = useState(400); // Default width
	const [isResizing, setIsResizing] = useState(false);
	const rightSidebarRef = useRef<HTMLDivElement>(null);
	const [selectedLocationIndex, setSelectedLocationIndex] = useState<
		number | null
	>(null);

	const MIN_RIGHT_WIDTH = 400;
	const MAX_RIGHT_WIDTH = 600;

	// Memoize userLocation and mapCenter to avoid unnecessary re-renders
	const memoizedUserLocation = useMemo(
		() =>
			userLocation
				? {
						latitude: userLocation.lat,
						longitude: userLocation.lng,
				  }
				: null,
		[userLocation?.lat, userLocation?.lng]
	);
	const memoizedMapCenter = useMemo(
		() =>
			memoizedUserLocation
				? [memoizedUserLocation.latitude, memoizedUserLocation.longitude]
				: [0, 0],
		[memoizedUserLocation]
	);

	// Transform extractedLocations to match DynamicLeafletMap interface
	const transformedExtractedLocations = useMemo(
		() =>
			extractedLocations.map((location) => ({
				name: (location.name as string) || 'Unknown Location',
				latitude:
					(location.latitude as number) || (location.lat as number) || 0,
				longitude:
					(location.longitude as number) || (location.lng as number) || 0,
				walk_route_distance_m: location.walk_route_distance_m as
					| number
					| undefined,
				address: location.address as string | undefined,
			})),
		[extractedLocations]
	);

	// High-performance resize handler - NO React state updates during drag
	const handleRightResize = useCallback(
		(e: MouseEvent) => {
			if (!isResizing || !rightSidebarRef.current) return;

			e.preventDefault();
			e.stopPropagation();

			// Calculate new width immediately - no requestAnimationFrame delay
			const rect = rightSidebarRef.current.getBoundingClientRect();
			const newWidth = rect.right - e.clientX;
			const clampedWidth = Math.max(
				MIN_RIGHT_WIDTH,
				Math.min(MAX_RIGHT_WIDTH, newWidth)
			);

			// INSTANT DOM update - bypasses React entirely
			rightSidebarRef.current.style.width = `${clampedWidth}px`;

			// NO React state updates during drag - only at the end
		},
		[isResizing]
	);

	const handleRightResizeEnd = useCallback(() => {
		// Re-enable interactions immediately
		document.body.classList.remove('resizing');

		// Get final width from DOM and sync with React state ONCE
		if (rightSidebarRef.current) {
			const finalWidth =
				parseInt(rightSidebarRef.current.style.width) || rightSidebarWidth;

			// Re-enable CSS transitions
			rightSidebarRef.current.style.transition = '';

			// Single React state update at the end
			setRightSidebarWidth(finalWidth);
		}

		// Set resizing to false LAST to prevent unnecessary re-renders
		setIsResizing(false);
	}, [rightSidebarWidth]);

	const handleRightResizeStart = useCallback((e: React.MouseEvent) => {
		e.preventDefault();
		e.stopPropagation();

		// Immediate DOM optimizations - no waiting for React
		document.body.classList.add('resizing');

		if (rightSidebarRef.current) {
			// Kill ALL transitions immediately for instant response
			rightSidebarRef.current.style.transition = 'none';
			rightSidebarRef.current.style.willChange = 'width'; // GPU optimization
		}

		// Set resizing state LAST to minimize re-renders
		setIsResizing(true);
	}, []);

	useEffect(() => {
		if (isResizing) {
			// Ultra-high performance event listeners
			const options = { passive: false, capture: true };

			document.addEventListener('mousemove', handleRightResize, options);
			document.addEventListener('mouseup', handleRightResizeEnd, options);
			document.addEventListener(
				'selectstart',
				(e) => e.preventDefault(),
				options
			);

			// Prevent context menu during resize
			document.addEventListener(
				'contextmenu',
				(e) => e.preventDefault(),
				options
			);

			return () => {
				document.removeEventListener('mousemove', handleRightResize, options);
				document.removeEventListener('mouseup', handleRightResizeEnd, options);
				document.removeEventListener(
					'selectstart',
					(e) => e.preventDefault(),
					options
				);
				document.removeEventListener(
					'contextmenu',
					(e) => e.preventDefault(),
					options
				);
			};
		}
	}, [isResizing, handleRightResize, handleRightResizeEnd]);

	// POI actions
	const handleViewDetails = (poi: Record<string, unknown>) => {
		window.open(`/pois/official/${poi.id}`, '_blank');
	};
	const handleCopyCoordinates = (poi: Record<string, unknown>) => {
		const coords = `${poi.latitude || poi.lat}, ${poi.longitude || poi.lng}`;
		navigator.clipboard.writeText(coords);
	};
	const handleOpenMaps = (poi: Record<string, unknown>) => {
		const lat = poi.latitude || poi.lat;
		const lng = poi.longitude || poi.lng;
		const mapsUrl = `https://www.google.com/maps/dir/?api=1&destination=${lat},${lng}`;
		window.open(mapsUrl, '_blank');
	};

	return (
		<>
			<ModalOverlay
				isOpen={isOpen}
				onClose={onClose}
			/>

			<div
				ref={rightSidebarRef}
				data-sidebar='right'
				className={`overflow-hidden flex flex-col ${
					zIndexLayers.sidebar
				} border-l relative
          ${isResizing ? '' : 'transition-all duration-300'}
          ${
						isOpen
							? 'fixed sm:relative inset-0 sm:inset-auto w-full h-full sm:h-auto'
							: 'w-0 sm:w-0 p-0'
					}
        `}
				style={{
					...sidebarStyles.right,
					width: isOpen ? `${rightSidebarWidth}px` : '0px',
					cursor: isResizing ? 'col-resize' : 'default',
					transition: isResizing ? 'none' : '',
					willChange: isResizing ? 'width' : 'auto',
					transform: isResizing ? 'translateZ(0)' : '',
				}}>
				{isOpen && (
					<>
						{/* Simple Header */}
						<div
							className='absolute top-0 left-0 right-0 z-10 p-3 flex justify-between items-center bg-white/90 backdrop-blur-sm'
							style={{
								borderBottom: `1px solid ${colors.ui.blue200}`,
							}}>
							<h3
								className='text-sm font-semibold'
								style={{ color: colors.brand.blue }}>
								Map & Locations
							</h3>
							<IconButton
								onClick={onClose}
								icon={<span className='text-lg'>×</span>}
								variant='default'
							/>
						</div>

						{/* Full Screen Map Container */}
						<div className='absolute inset-0 pt-12 flex flex-col'>
							{/* Map */}
							<div className='flex-1 min-h-[250px]'>
								{!isResizing && (
									<div className='w-full h-full'>
										<DynamicLeafletMap
											userLocation={memoizedUserLocation}
											extractedLocations={transformedExtractedLocations}
											mapCenter={memoizedMapCenter as [number, number]}
											zoom={12}
											selectedLocationIndex={selectedLocationIndex}
										/>
									</div>
								)}
								{isResizing && (
									<div
										className='absolute inset-0 flex items-center justify-center'
										style={{
											backgroundColor: colors.ui.blue50,
											border: `2px dashed ${colors.ui.blue200}`,
											zIndex: 9999, // Ensure it's on top
										}}>
										<div className='text-center pointer-events-none'>
											<div
												className='text-3xl mb-3 animate-pulse'
												style={{ color: colors.brand.blue }}>
												↔️
											</div>
											<div
												className='text-lg font-semibold mb-1'
												style={{ color: colors.neutral.slateGray }}>
												Resizing Sidebar
											</div>
											<div
												className='text-sm'
												style={{ color: colors.neutral.slateGray }}>
												Map will reappear when done
											</div>
										</div>
									</div>
								)}
							</div>
							{/* POI List */}
							{extractedLocations && extractedLocations.length > 0 && (
								<div className='overflow-y-auto max-h-64 border-t border-gray-200 bg-white/95 px-2 py-2'>
									<h4 className='text-xs font-semibold mb-2 text-gray-700'>
										Locations
									</h4>
									<ul className='space-y-2'>
										{extractedLocations.map(
											(poi: Record<string, unknown>, idx: number) => (
												<li
													key={`${poi.id || 'poi'}-${idx}`}
													className={`rounded-lg px-3 py-2 flex flex-col gap-1 cursor-pointer transition-all border ${
														selectedLocationIndex === idx
															? 'border-blue-500 bg-blue-50'
															: 'border-transparent hover:bg-gray-50'
													}`}
													onClick={() => setSelectedLocationIndex(idx)}>
													<div className='flex flex-col gap-2'>
														<span
															className='font-medium text-sm'
															title={poi.name as string}>
															{poi.name as string}
														</span>
														<div className='flex flex-col gap-1'>
															<button
																title='View Details'
																onClick={(e) => {
																	e.stopPropagation();
																	handleViewDetails(poi);
																}}
																className='flex items-center gap-2 text-blue-600 hover:text-blue-800 px-2 py-1 rounded text-xs hover:bg-blue-50 transition-colors'>
																<span>🔎</span>
																<span>View Details</span>
															</button>
															<button
																title='Copy Coordinates'
																onClick={(e) => {
																	e.stopPropagation();
																	handleCopyCoordinates(poi);
																}}
																className='flex items-center gap-2 text-gray-600 hover:text-gray-900 px-2 py-1 rounded text-xs hover:bg-gray-50 transition-colors'>
																<span>📋</span>
																<span>Copy Coordinates</span>
															</button>
															<button
																title='Open in Maps'
																onClick={(e) => {
																	e.stopPropagation();
																	handleOpenMaps(poi);
																}}
																className='flex items-center gap-2 text-green-600 hover:text-green-800 px-2 py-1 rounded text-xs hover:bg-green-50 transition-colors'>
																<span>🗺️</span>
																<span>Open in Maps</span>
															</button>
														</div>
													</div>
													{(poi.address as string) && (
														<div className='text-xs text-gray-500 truncate'>
															{poi.address as string}
														</div>
													)}
													{(poi.walk_route_distance_m as number) && (
														<div className='text-xs text-gray-400'>
															{(poi.walk_route_distance_m as number) < 1000
																? `${Math.round(
																		poi.walk_route_distance_m as number
																  )}m`
																: `${(
																		(poi.walk_route_distance_m as number) / 1000
																  ).toFixed(1)}km`}{' '}
															walk
														</div>
													)}
													{(poi.confidence as number) && (
														<div className='text-xs text-gray-400'>
															Confidence:{' '}
															{Math.round((poi.confidence as number) * 100)}%
														</div>
													)}
												</li>
											)
										)}
									</ul>
								</div>
							)}
						</div>

						{/* Resize Handle - Enhanced for better visibility */}
						<div
							className='resize-handle absolute top-0 left-0 w-3 h-full cursor-col-resize transition-all duration-200 group z-[60]'
							onMouseDown={(e) => handleRightResizeStart(e)}
							title='Resize sidebar'
							style={{
								backgroundColor: isResizing ? colors.brand.blue : 'transparent',
								borderRight: isResizing
									? `2px solid ${colors.brand.blue}`
									: 'none',
							}}>
							<div
								className='w-full h-full transition-all duration-200'
								style={{
									backgroundColor: isResizing
										? colors.brand.blue
										: 'transparent',
								}}
								onMouseEnter={(e) => {
									if (!isResizing) {
										e.currentTarget.style.backgroundColor = `${colors.brand.blue}40`;
									}
								}}
								onMouseLeave={(e) => {
									if (!isResizing) {
										e.currentTarget.style.backgroundColor = 'transparent';
									}
								}}
							/>
						</div>
					</>
				)}
			</div>
		</>
	);
};

export default RightSidebar;
