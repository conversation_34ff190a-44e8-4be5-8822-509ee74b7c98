/** @format */

import { colors } from '@/app/colors';
import React from 'react';
import { useSectionTransition } from '../utils/scrollAnimations';
import AnimatedGeometry from './AnimatedGeometry';
import DynamicSectionContainer from './DynamicSectionContainer';
import AIShowcase from './features/AIShowcase';
import CategoryExplorer from './features/CategoryExplorer';
import TechnicalShowcase from './features/TechnicalShowcase';
import LandingFooter from './footer/LandingFooter';
import HeroSection from './hero/HeroSection';
import WaveTransition from './WaveTransition';

interface LandingPageProps {
	onGetStarted: () => void;
}

const LandingPage: React.FC<LandingPageProps> = ({ onGetStarted }) => {
	// Section transition animations
	const heroTransition = useSectionTransition('hero', 'fade-scale');
	const categoryTransition = useSectionTransition('category', 'slide-diagonal');
	const techTransition = useSectionTransition('tech', 'fade-scale');
	const aiTransition = useSectionTransition('ai', 'wave-wash');
	const footerTransition = useSectionTransition('footer', 'fade-scale');

	return (
		<div
			className='min-h-screen relative overflow-hidden'
			style={{
				background: `
          linear-gradient(180deg,
            rgba(1, 3, 79, 0.2) 0%,
            rgba(163, 247, 181, 0.2) 25%,
            rgba(128, 237, 153, 0.2) 50%,
            rgba(102, 208, 255, 0.2) 75%,
            rgba(51, 194, 255, 0.2) 100%
          ),
          ${colors.neutral.cloudWhite}
        `,
			}}>
			{/* Parallax Background Effects */}
			<div className='fixed inset-0 pointer-events-none'>
				{/* Floating geometric shapes that move with scroll */}
				{Array.from({ length: 12 }).map((_, i) => (
					<div
						key={i}
						className='absolute rounded-full opacity-20 animate-pulse'
						style={{
							left: `${5 + i * 8}%`,
							top: `${10 + i * 12}%`,
							width: `${15 + i * 3}px`,
							height: `${15 + i * 3}px`,
							background:
								i % 3 === 0
									? colors.brand.blue + '40'
									: i % 3 === 1
									? colors.brand.green + '40'
									: colors.supporting.lightBlue + '40',
							animationDelay: `${i * 0.5}s`,
							animationDuration: `${3 + i * 0.2}s`,
						}}
					/>
				))}
			</div>
			{/* Hero Section - Centered for Brand Focus */}
			<section className='py-8 md:py-12 lg:py-16'>
				<DynamicSectionContainer sectionType='hero'>
					<div ref={heroTransition.elementRef}>
						<HeroSection onGetStarted={onGetStarted} />
					</div>
				</DynamicSectionContainer>
			</section>

			{/* Wave Transition from Hero to Features - Bottom to Top */}
			<div className='relative'>
				<WaveTransition
					isActive={categoryTransition.isVisible}
					direction='up'
					color={colors.brand.blue}
				/>
				{/* Geometric shapes animation */}
				<AnimatedGeometry
					isVisible={categoryTransition.isVisible}
					shapes={[
						{
							type: 'circle',
							size: 40,
							x: 20,
							y: 30,
							color: colors.brand.blue + '40',
							delay: 0,
						},
						{
							type: 'triangle',
							size: 30,
							x: 80,
							y: 20,
							color: colors.brand.green + '40',
							delay: 200,
						},
						{
							type: 'square',
							size: 25,
							x: 60,
							y: 50,
							color: colors.supporting.lightBlue + '40',
							delay: 400,
						},
					]}
				/>
			</div>

			{/* Features Section - Category Explorer (Left Aligned) */}
			<section className='py-8 md:py-12 lg:py-16'>
				<DynamicSectionContainer sectionType='section1'>
					<div
						ref={categoryTransition.elementRef}
						className={`transition-all duration-800 ${
							categoryTransition.animationState === 'idle'
								? 'opacity-0 transform translate-x-12 translate-y-8'
								: categoryTransition.animationState === 'entering'
								? 'opacity-70 transform translate-x-6 translate-y-4'
								: 'opacity-100 transform translate-x-0 translate-y-0'
						}`}>
						<CategoryExplorer
							onCategorySelect={(category) =>
								console.log('Selected category:', category)
							}
						/>
					</div>
				</DynamicSectionContainer>
			</section>

			{/* Wave Transition between Features - Bottom to Top */}
			<div className='relative'>
				<WaveTransition
					isActive={aiTransition.isVisible}
					direction='up'
					color={colors.brand.green}
				/>
				{/* Geometric shapes animation */}
				<AnimatedGeometry
					isVisible={aiTransition.isVisible}
					shapes={[
						{
							type: 'triangle',
							size: 35,
							x: 15,
							y: 40,
							color: colors.brand.green + '40',
							delay: 100,
						},
						{
							type: 'circle',
							size: 45,
							x: 75,
							y: 25,
							color: colors.brand.blue + '40',
							delay: 300,
						},
						{
							type: 'square',
							size: 28,
							x: 45,
							y: 60,
							color: colors.brand.navy + '40',
							delay: 500,
						},
					]}
				/>
			</div>

			{/* Features Section - AI Showcase (Right Aligned) */}
			<DynamicSectionContainer sectionType='section2'>
				<div
					ref={aiTransition.elementRef}
					className={`transition-all duration-1000 ${
						aiTransition.animationState === 'idle'
							? 'opacity-0 transform translate-x-8 scale-95'
							: aiTransition.animationState === 'entering'
							? 'opacity-70 transform translate-x-4 scale-98'
							: 'opacity-100 transform translate-x-0 scale-100'
					}`}>
					<AIShowcase onGetStarted={onGetStarted} />
				</div>
			</DynamicSectionContainer>

			{/* Wave Transition to Technical Section */}
			<div className='relative'>
				<WaveTransition
					isActive={techTransition.isVisible}
					direction='up'
					color={colors.brand.navy}
				/>
				<AnimatedGeometry
					isVisible={techTransition.isVisible}
					shapes={[
						{
							type: 'square',
							size: 32,
							x: 25,
							y: 35,
							color: colors.brand.navy + '40',
							delay: 0,
						},
						{
							type: 'circle',
							size: 38,
							x: 70,
							y: 45,
							color: colors.brand.blue + '40',
							delay: 200,
						},
					]}
				/>
			</div>

			{/* Technical Showcase Section */}
			<section className='py-8 md:py-12 lg:py-16'>
				<DynamicSectionContainer sectionType='section1'>
					<div
						ref={techTransition.elementRef}
						className={`transition-all duration-800 ${
							techTransition.animationState === 'idle'
								? 'opacity-0 transform translate-y-12 scale-95'
								: techTransition.animationState === 'entering'
								? 'opacity-70 transform translate-y-6 scale-98'
								: 'opacity-100 transform translate-y-0 scale-100'
						}`}>
						<TechnicalShowcase onGetStarted={onGetStarted} />
					</div>
				</DynamicSectionContainer>
			</section>

			{/* Wave Transition to Footer - Bottom to Top */}
			<div className='relative'>
				<WaveTransition
					isActive={footerTransition.isVisible}
					direction='up'
					color={colors.brand.navy}
				/>
			</div>

			{/* Footer Section - Centered */}
			<section className='py-8 md:py-12 lg:py-16'>
				<DynamicSectionContainer sectionType='footer'>
					<div
						ref={footerTransition.elementRef}
						className={`transition-all duration-800 ${
							footerTransition.animationState === 'idle'
								? 'opacity-0 transform scale-90'
								: footerTransition.animationState === 'entering'
								? 'opacity-50 transform scale-95'
								: 'opacity-100 transform scale-100'
						}`}>
						<LandingFooter />
					</div>
				</DynamicSectionContainer>
			</section>
		</div>
	);
};

export default LandingPage;
