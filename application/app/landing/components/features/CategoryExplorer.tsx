/** @format */

'use client';

import { colors } from '@/app/colors';
import { useViewportHeight } from '@/app/landing/utils/responsiveUtils';
import { LANDING_PAGE_DATA } from '@/app/shared/poi/constants';
import React from 'react';
import { FiMapPin } from 'react-icons/fi';
import DynamicCategoryMosaic from './DynamicCategoryMosaic';

interface CategoryExplorerProps {
	onCategorySelect?: (category: any) => void;
}

const CategoryExplorer: React.FC<CategoryExplorerProps> = ({
	onCategorySelect,
}) => {
	// Use viewport height for responsive section sizing
	const { screenSize, minHeight, padding } = useViewportHeight('section');

	return (
		<div
			className='bg-transparent viewport-section'
			style={{
				minHeight,
				maxHeight: 'calc(100vh - 100px)', // Reserve space for wave at bottom (100px wave height)
				paddingLeft: 0,
				paddingRight: 0,
				paddingTop: `${parseInt(padding) + 40}px`, // Add extra top padding to clear the wave
				paddingBottom: '120px', // Extra padding to prevent overlap with bottom wave
				overflow: 'hidden',
			}}>
			{/* Container with height constraints */}
			<div className='h-full flex flex-col'>
				{/* Main Content Area - Two Column Layout */}
				<div className='flex-1 grid grid-cols-1 lg:grid-cols-5 gap-6 lg:gap-8 min-h-0'>
					{/* Left Column - Filters and Cards (Expanded) */}
					<div className='lg:col-span-3 flex flex-col min-h-0'>
						<DynamicCategoryMosaic onCategorySelect={onCategorySelect} />
					</div>

					{/* Right Column - Title and Info Panel */}
					<div className='lg:col-span-2 flex flex-col'>
						{/* Background accent shapes */}
						<div className='relative mb-6 flex-shrink-0'>
							<div className='absolute inset-0 opacity-10'>
								<div
									className='absolute top-0 right-0 w-16 h-16 rounded-full'
									style={{
										background: `radial-gradient(circle, ${colors.brand.blue}40 0%, transparent 70%)`,
									}}
								/>
								<div
									className='absolute top-4 left-1/4 w-10 h-10 rounded-lg rotate-45'
									style={{
										background: `linear-gradient(45deg, ${colors.brand.green}30 0%, transparent 70%)`,
									}}
								/>
							</div>

							{/* Header - Right Aligned */}
							<div className='relative text-right'>
								<div className='flex justify-end mb-4'>
									<div
										className='inline-flex items-center space-x-2 rounded-full px-4 py-2 border backdrop-blur-sm'
										style={{
											background: `linear-gradient(135deg, ${colors.ui.green50}80 0%, ${colors.ui.blue50}80 100%)`,
											borderColor: colors.ui.gray200,
											boxShadow: '0 4px 16px rgba(51, 194, 255, 0.08)',
										}}>
										<FiMapPin
											className='w-4 h-4'
											style={{ color: colors.brand.blue }}
										/>
										<span
											className='text-sm font-medium'
											style={{ color: colors.neutral.textBlack }}>
											{LANDING_PAGE_DATA.sectionHeaders.categoryExplorer.badge}
										</span>
									</div>
								</div>

								<h2 className='text-2xl lg:text-3xl xl:text-4xl font-bold leading-tight'>
									<span style={{ color: colors.brand.navy }}>
										{LANDING_PAGE_DATA.sectionHeaders.categoryExplorer.title}
									</span>
									<br />
									<span
										className='text-transparent bg-clip-text'
										style={{
											backgroundImage: `linear-gradient(135deg, ${colors.brand.blue} 0%, ${colors.brand.green} 100%)`,
										}}>
										{LANDING_PAGE_DATA.sectionHeaders.categoryExplorer.subtitle}
									</span>
								</h2>
							</div>
						</div>

						{/* Info Panel */}
						<div className='flex-shrink-0'>
							<div
								className='p-6 lg:p-8 rounded-2xl'
								style={{
									background: `linear-gradient(135deg, ${colors.neutral.cloudWhite}90 0%, ${colors.ui.green50}30 100%)`,
								}}>
								<p
									className='text-base leading-relaxed font-medium'
									style={{ color: colors.neutral.slateGray }}>
									{
										LANDING_PAGE_DATA.sectionHeaders.categoryExplorer
											.description
									}
								</p>

								{/* Compact feature highlights */}
								<div className='mt-6 space-y-3'>
									<div className='flex items-center space-x-3'>
										<div
											className='w-6 h-6 rounded-full flex items-center justify-center'
											style={{ background: `${colors.brand.blue}20` }}>
											<span className='text-xs'>🎯</span>
										</div>
										<span
											className='text-sm font-medium'
											style={{ color: colors.neutral.slateGray }}>
											Smart filtering
										</span>
									</div>
									<div className='flex items-center space-x-3'>
										<div
											className='w-6 h-6 rounded-full flex items-center justify-center'
											style={{ background: `${colors.brand.green}20` }}>
											<span className='text-xs'>📍</span>
										</div>
										<span
											className='text-sm font-medium'
											style={{ color: colors.neutral.slateGray }}>
											Location-based
										</span>
									</div>
									<div className='flex items-center space-x-3'>
										<div
											className='w-6 h-6 rounded-full flex items-center justify-center'
											style={{ background: `${colors.brand.blue}20` }}>
											<span className='text-xs'>⭐</span>
										</div>
										<span
											className='text-sm font-medium'
											style={{ color: colors.neutral.slateGray }}>
											Curated results
										</span>
									</div>
								</div>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
	);
};

export default CategoryExplorer;
